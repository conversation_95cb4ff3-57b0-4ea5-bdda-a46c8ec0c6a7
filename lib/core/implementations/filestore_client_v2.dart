import 'dart:async';
import 'dart:io';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:web_socket_channel/io.dart';
import 'package:path/path.dart';
import 'package:dio/dio.dart';
import 'package:mime/mime.dart';

import '../network/web_socket/web_socket_state.dart';
import 'upload_file.dart';
import 'upload_state.dart';
import '../network/http_client_manager.dart';
import '../network/web_socket/web_socket_manager.dart';
import '../utils/helpers/cache_manager.dart';
import '../data/models/api_response_classes/complete_file_chunk_meta.dart';
import '../data/models/api_response_classes/complete_file_meta.dart';
import '../data/models/api_response_classes/file_meta.dart';
import '../data/models/api_response_classes/fs_file_chunk_upload_response.dart';
import '../data/models/api_response_classes/fs_get_chunk_uploaded_response.dart';
import '../data/models/api_response_classes/fs_init_file_upload_response.dart';
import '../data/models/api_response_classes/get_file_chunk_meta.dart';
import '../data/models/api_response_classes/init_file_chunk_upload_meta.dart';
import '../utils/constants/config.dart';
import '../data/models/api_response_classes/file_chunk_meta.dart';
import '../data/models/api_response_classes/fs_base_response.dart';
import '../utils/exceptions/file_store_exception.dart';
import '../interfaces/filestore_client_interface.dart';
import '../network/fs_network_manager.dart';
import '../utils/constants/enums.dart';

class FilestoreClientV2 implements IFilestoreClient {
  final HttpClientManager httpClientManager;
  late final Dio client;
  final String baseUrl;
  final String? token;
  final String? folderPath;
  final String openConnectURL;
  late int limitFileSizeByMb;
  late List<String>? limitFileType;

  void logDebug(String message) => AppConfig.printDebugMode(message);

  final FSNetworkManager fsNetworkManager = FSNetworkManager();
  final CacheManager cacheManager = CacheManager();

  final WebSocketManager _webSocketManager = WebSocketManager();

  final String defaultKey = "ws_${DateTime.now().millisecondsSinceEpoch}";

  /// Optimized method to read chunk directly from file without loading entire file into memory
  Future<Uint8List> _readChunk(File file, int offset, int size) async {
    final raf = await file.open();
    try {
      await raf.setPosition(offset);
      final chunk = await raf.read(size);
      return chunk;
    } finally {
      await raf.close();
    }
  }

  /// Calculate MD5 for chunk in isolate to avoid blocking UI thread
  static String _calculateMd5ForChunk(Map<String, dynamic> params) {
    final filePath = params['filePath'] as String;
    final chunkIndex = params['chunkIndex'] as int;
    final chunkSize = params['chunkSize'] as int;

    final file = File(filePath);
    final offset = chunkIndex * chunkSize;
    final raf = file.openSync();
    try {
      raf.setPositionSync(offset);
      final chunkData = raf.readSync(chunkSize);
      return AppConfig.generateMd5(chunkData);
    } finally {
      raf.closeSync();
    }
  }

  FilestoreClientV2({
    required this.baseUrl,
    this.token,
    this.folderPath,
    required this.openConnectURL,
    int? limitFileSizeByMb,
    this.limitFileType,
  }) : httpClientManager = HttpClientManager(baseUrl: baseUrl, token: token) {
    client = httpClientManager.dio;
    this.limitFileSizeByMb = limitFileSizeByMb ?? 0;
  }

  /// Uploads a file to the server using a WebSocket connection for files under 5 MB.
  /// For files larger than 5 MB, the function uses `handleFileOver5Mb` for chunked uploads.
  ///
  /// This function initiates a WebSocket connection using a URL from `getConnectUrl`, prepares the file data,
  /// and sends it in one transmission for small files. If the upload times out (no message received within the
  /// specified duration), it closes the WebSocket and completes with an error message. The function listens to
  /// WebSocket messages to handle the upload status and to complete or return errors as `BaseResponse`.
  ///
  /// Parameters:
  /// - `objFile`: The `UpFile` object representing the file to upload.
  /// - `progress`: A callback function to report upload progress as a percentage.
  /// - `cancelToken`: A token to cancel the upload request if needed.
  ///
  /// Returns:
  /// - `Future<FileUploadResponse>`: A future that completes with a `BaseResponse` containing the upload result.
  ///   If successful, the `data` contains the file URL; if an error occurs, the `message` describes the error.
  ///
  /// Error Handling:
  /// - Uses `timeoutListenWebsocketDuration` to limit waiting time for messages from the server.
  /// - On timeout or errors during the upload, completes with `FileUploadResponse.error`.
  /// - If the file exceeds 5 MB, `handleFileOver5Mb` is used to handle chunked uploading.
  ///

  @override
  Future<void> uploadFile(
    UpFile objFile, {
    void Function(UpFile objFile, String fileUrl)? onSuccess,
    void Function(UpFile objFile, ErrorCode code, String errorMessage)? onError,
    void Function(double progress)? onProgress,
    Future<String?> Function()? getTokenCallback,
    CancelToken? cancelToken,
    String? token,
    String? folderPath,
  }) async {
    logDebug("Uploading file: ${objFile.name}");
    String filePath = "";

    /// Config upload file by fileData Uint8List
    if (objFile.fileData != null) {
      filePath = await cacheManager.saveFileToCacheByData(objFile.fileData!);
    } else {
      filePath = objFile.path;
    }

    File file = File(filePath);
    int fileSize = file.lengthSync();
    String? fileType = lookupMimeType(file.path);
    objFile.size = fileSize;
    token = token ?? this.token;
    folderPath = folderPath ?? this.folderPath;

    final String uploadKey = objFile.key ?? defaultKey;
    final String uploadRef = objFile.ref ?? AppConfig.generateUniqueId();
    late WebSocketState? wsState;

    if (uploadKey.isEmpty) {
      onError?.call(objFile, ErrorCode.uploadError, "Invalid file key");
      return;
    }

    if (_isNotValidFileType(fileType)) {
      onError?.call(objFile, ErrorCode.uploadError, "Invalid file type");
      return;
    }

    if (_isLimitExceededFileSize(fileSize)) {
      onError?.call(
        objFile,
        ErrorCode.uploadError,
        "File upload limit exceeded ${limitFileSizeByMb}MB",
      );
      return;
    }

    Future<String> getConnectUrl() async => await _getConnectUrl(
          token ?? '',
          folderPath ?? '',
          getTokenCallback,
        );

    /// Check objFile.key
    /// If _webSocketManager.isConnected & key == last key, use ws exited. Else, create new connection.
    ///
    wsState = await _webSocketManager.connect(
      uploadKey,
      uploadRef,
      getConnectUrl,
      headers: {"Authorization": "Bearer $token"},
    );

    final uploadState = UploadState(
      offset: 0,
      chunkIndex: 0,
      totalSize: file.lengthSync(),
      chunkSize: AppConfig.chunkSize,
      cachedFilePath: file.path,
      wsState: wsState,
      ref: uploadRef,
    );

    try {
      /// Send file small
      if (fileSize < AppConfig.fiveMb) {
        await _handleFileSmaller5Mb(
          file,
          onError,
          objFile,
          cancelToken,
          onSuccess,
          folderPath!,
          uploadState,
        );
        return;
      }

      /// Send file over 5mb
      _handleFileOver5Mb(
        file,
        onProgress,
        onSuccess,
        onError,
        objFile,
        cancelToken,
        folderPath!,
        uploadState,
      );
    } catch (e) {
      logDebug("Error during WebSocket upload: $e");
      if (e is SocketException) {
        onError?.call(objFile, ErrorCode.uploadError, e.message);
      } else {
        onError?.call(objFile, ErrorCode.unknown, e.toString());
      }
    }
  }

  void _listenConnectivityChanged(
    File file,
    dynamic onError,
    UpFile objFile,
    UploadState? uploadState,
  ) {
    fsNetworkManager.onConnectivityChanged.listen((result) {
      logDebug("Connection changed $result");
      if (fsNetworkManager.isNone) {
        logDebug("Disconnected. Pausing upload...");

        _handleUploadError(
          file,
          objFile,
          uploadState,
          onError,
          ErrorCode.noInternet,
          "Device is disconnect. Close upload.",
        );
      }
    });
  }

  void _listenUploadCanceledByUser(
    File file,
    dynamic onError,
    UpFile objFile,
    UploadState? uploadState,
  ) {
    _handleUploadError(
      file,
      objFile,
      uploadState,
      onError,
      ErrorCode.noInternet,
      "Upload canceled by user.. Close upload.",
    );
  }

  Future<String> _getConnectUrl(
    String token,
    String folderPath,
    Future<String?> Function()? getTokenCallback,
  ) async {
    try {
      String? updatedToken = token;
      if (updatedToken.isEmpty && getTokenCallback != null) {
        updatedToken = await getTokenCallback();
      }

      if (updatedToken == null || updatedToken.isEmpty) {
        throw FileStoreException("Invalid token");
      }

      Response response = await httpClientManager.get(
        openConnectURL,
        queryParameters: {'folderPath': folderPath},
        headers: {
          "x-session-token": updatedToken,
        },
      );

      final fsBaseResponse = FSBaseResponse<FSData>.fromJson(
        jsonDecode(response.toString()),
        (json) => FSData.fromJson(json as Map<String, dynamic>),
      );

      logDebug("Received URL: ${fsBaseResponse.data?.url}");
      return fsBaseResponse.data?.url ?? "";
    } catch (e) {
      if (e is DioException) {
        httpClientManager.handleDioError(e);
      } else {
        logDebug("Unexpected error: $e");
        throw FileStoreException("Unexpected error occurred: $e");
      }
      return "";
    }
  }

  @override
  Future<void> resumedFile(
    UpFile objFile, {
    void Function(UpFile objFile, String fileUrl)? onSuccess,
    void Function(UpFile objFile, ErrorCode code, String errorMessage)? onError,
    void Function(double progress)? onProgress,
    Future<String?> Function()? getTokenCallback,
    CancelToken? cancelToken,
    String? token,
    String? folderPath,
  }) async {
    objFile.uploadState;

    late DateTime startTime, endTime;
    try {
      folderPath = folderPath ?? this.folderPath;
      token = token ?? this.token;

      logDebug(
        "Resume cachedFilePath: ${objFile.uploadState!.cachedFilePath}",
      );

      File file = File(objFile.uploadState!.cachedFilePath ?? objFile.path);

      final fileBytes = await file.readAsBytes();
      final fileSize = file.lengthSync();
      objFile.size = fileSize;

      final uploadState = UploadState(
        offset: 0,
        chunkIndex: 0,
        totalSize: file.lengthSync(),
        chunkSize: AppConfig.chunkSize,
        cachedFilePath: file.path,
      );

      if (fileSize < AppConfig.fiveMb) {
        _handleFileSmaller5Mb(
          file,
          onError,
          objFile,
          cancelToken,
          onSuccess,
          folderPath!,
          uploadState,
        );
      } else {
        logDebug(
          "Resume chunkIndex: ${objFile.uploadState?.chunkIndex}",
        );

        final uploadState = objFile.uploadState!;
        final connectUrl =
            await _getConnectUrl(token!, folderPath!, getTokenCallback);
        logDebug(uploadState.uploadChunks!.last.toString());

        final channel = await _connectWithTimeout(
          connectUrl,
          AppConfig.timeoutConnectWebsocketDuration,
          headers: {
            "x-session-token": token,
          },
        );

        await channel.ready;
        uploadState.webSocketUrl = connectUrl;
        _listenConnectivityChanged(
          file,
          onError,
          objFile,
          uploadState,
        );
        cancelToken?.whenCancel.then((_) {
          logDebug("Upload canceled by user.");
          _listenUploadCanceledByUser(
            file,
            onError,
            objFile,
            uploadState,
          );
        });

        final getFileChunkMeta = GetFileChunkMeta(
          type: UploadType.getUploadChunks,
          uploadId: uploadState.uploadId!,
        );
        logDebug("Resume - getUploadChunks: ${uploadState.toString()}");
        channel.sink.add(getFileChunkMeta.toJsonEncode());

        channel.stream.timeout(
          AppConfig.timeoutListenWebsocketDuration,
          onTimeout: (sink) {
            logDebug("Timeout: No message received within 30 seconds.");
            onError?.call(
              objFile,
              ErrorCode.timeoutListenWebsocket,
              "Timeout: No message received within 30 seconds.",
            );

            sink.close();
          },
        ).listen(
          (msg) async {
            logDebug("msg: $msg");
            Map<String, dynamic> jsonResponse = jsonDecode(msg);

            final response = FSFileChunkUploadResponse.fromJson(jsonResponse);
            final actionType = UploadType.fromString(response.type);

            switch (actionType) {
              case UploadType.getUploadChunks:
                final getChunkUploadedData =
                    FSGetChunkUploadedData.fromJson(response.data);

                if (getChunkUploadedData.ok == false) {
                  _handleUploadError(
                    file,
                    objFile,
                    uploadState,
                    onError,
                    ErrorCode.uploadError,
                    "${getChunkUploadedData.error?.message}: ${getChunkUploadedData.error?.details}",
                  );
                } else {
                  List uploadChunks = getChunkUploadedData.data!;

                  final lastChunk = uploadChunks
                      .where((uc) => uc is FSUploadChunk && uc.isCompleted)
                      .cast<FSUploadChunk>()
                      .lastOrNull;

                  uploadState.chunkIndex =
                      lastChunk == null ? 0 : lastChunk.chunkIndex + 1;

                  uploadState.offset = 0;
                  if (lastChunk != null) {
                    final lastChunkClient = uploadState.uploadChunks!
                        .where((c) => c.chunkIndex == lastChunk.chunkIndex)
                        .singleOrNull;
                    if (lastChunkClient != null) {
                      uploadState.offset =
                          lastChunkClient.offset + AppConfig.chunkSize;
                    }
                  }

                  startTime = DateTime.now();
                  logDebug("Resume at data: ${uploadState.toString()}");

                  _uploadFileChunk(uploadState, fileBytes, channel, onProgress);
                }

                break;
              case UploadType.fileChunkUpload:
                final fileChunkUploadData =
                    FSFileChunkUploadData.fromJson(response.data);

                if (fileChunkUploadData.ok == false) {
                  _handleUploadError(
                    file,
                    objFile,
                    uploadState,
                    onError,
                    ErrorCode.uploadError,
                    "${fileChunkUploadData.error?.message}: ${fileChunkUploadData.error?.details}",
                  );
                } else {
                  if (uploadState.offset == AppConfig.chunkSize) {
                    endTime = DateTime.now();
                    uploadState.setUploadSpeed(startTime, endTime);
                  }

                  List uploadChunks = fileChunkUploadData.data!.uploadChunks;
                  uploadState.uploadChunks?.last.isCompleted = true;
                  if (uploadChunks.length < uploadState.totalChunks) {
                    _uploadFileChunk(
                      uploadState,
                      fileBytes,
                      channel,
                      onProgress,
                    );
                  } else if (uploadChunks
                          .where((uc) => uc is FSUploadChunk && uc.isCompleted)
                          .cast<FSUploadChunk>()
                          .length ==
                      uploadState.totalChunks) {
                    // completeFileChunkUpload
                    final completeFileChunkMeta = CompleteFileChunkMeta(
                      type: UploadType.completeFileChunkUpload,
                      uploadId: uploadState.uploadId!,
                      s3UploadId: uploadState.s3UploadId!,
                    );
                    channel.sink.add(completeFileChunkMeta.toJsonEncode());
                  }
                }
                break;
              case UploadType.completeFileChunkUpload:
                final completeFileChunkData =
                    FSCompleteFileChunkData.fromJson(response.data);
                if (completeFileChunkData.ok == false) {
                  _handleUploadError(
                    file,
                    objFile,
                    uploadState,
                    onError,
                    ErrorCode.uploadError,
                    "${completeFileChunkData.error?.message}: ${completeFileChunkData.error?.details}",
                  );
                } else {
                  final s3Url = completeFileChunkData.data?.s3Url;
                  if (uploadState.cachedFilePath != null) {
                    cacheManager
                        .removeFileFromCacheList(uploadState.cachedFilePath!);
                  }
                  channel.sink.close();
                  objFile.metaData = completeFileChunkData.data!;
                  onSuccess?.call(objFile, s3Url!);
                }
                break;
              default:
            }
          },
          onError: (error) {
            onError?.call(objFile, ErrorCode.unknown, error.toString());
          },
        );
      }
    } catch (e) {
      logDebug("Expectation caught: $e");
      onError?.call(objFile, ErrorCode.unknown, e.toString());
    }
  }

  // Private Upload

  Future<void> _handleFileSmaller5Mb(
    File file,
    onError,
    UpFile objFile,
    CancelToken? cancelToken,
    onSuccess,
    String folderPath,
    UploadState uploadState,
  ) async {
    String uploadKey = objFile.key ?? defaultKey;
    _listenConnectivityChanged(file, onError, objFile, uploadState);

    cancelToken?.whenCancel.then((_) {
      logDebug("Upload canceled by user.");
      _listenUploadCanceledByUser(file, onError, objFile, uploadState);
    });

    Uint8List bytes = objFile.fileData != null
        ? objFile.fileData as Uint8List
        : await file.readAsBytes();

    final fileMeta = FileMeta(
      type: UploadType.initFileUpload,
      name: basename(file.path),
      md5FileHash: AppConfig.generateMd5(bytes),
      fileData: bytes,
      ref: uploadState.ref,
    );

    errorListener(dynamic error) =>
        _errorListener(uploadState.ref ?? '', error, onError, objFile);

    late final Function(String) messageListener;
    messageListener = (String message) {
      Map<String, dynamic> jsonResponse = jsonDecode(message);
      //.. only listen by ref
      if (jsonResponse['ref'] != uploadState.ref) return;
      logDebug("Response for smaller file upload: $message");
      final response = FSInitFileUploadResponse.fromJson(jsonResponse);
      final type = UploadType.fromString(response.type);

      switch (type) {
        case UploadType.initFileUpload:
          _handleInitFileUploadResponse(
            uploadKey,
            response,
            objFile,
            onError,
            uploadState,
          );
          break;

        case UploadType.completeFileUpload:
          _handleCompleteFileUploadResponse(
            response,
            objFile,
            onSuccess,
            onError,
            uploadState,
          );

          uploadState.wsState?.status == WebSocketStatus.idle;
          _webSocketManager.updateConnectionsActivityByCondition(
            //_handleFileSmaller5Mb
            uploadKey,
            WebSocketStatus.idle,
            ((state) => state.ref == uploadState.ref),
          );

          break;

        default:
          logDebug("Unknown response type: ${response.type}");
          break;
      }
    };

    _webSocketManager.addErrorListener(uploadKey, errorListener);
    _webSocketManager.sendMessage(
      uploadKey,
      uploadState.ref ?? '',
      fileMeta.toJsonEncode(),
    );
    _webSocketManager.addMessageListener(uploadKey, messageListener);
  }

  void _handleInitFileUploadResponse(
    String uploadKey,
    FSInitFileUploadResponse response,
    UpFile objFile,
    void Function(UpFile objFile, ErrorCode code, String errorMessage)? onError,
    UploadState uploadState,
  ) {
    if (response.data.ok == false) {
      // Gọi callback `onError` nếu lỗi
      onError?.call(
        objFile,
        ErrorCode.uploadError,
        "${response.data.error?.message}: ${response.data.error?.details}",
      );
    } else {
      final uploadId = response.data.data?.uploadId;
      if (uploadId != null) {
        final completeFileMeta = CompleteFileMeta(
          type: UploadType.completeFileUpload,
          uploadId: uploadId,
          ref: uploadState.ref ?? '',
        );

        _webSocketManager.sendMessage(
          uploadKey,
          uploadState.ref ?? '',
          completeFileMeta.toJsonEncode(),
        );
      } else {
        logDebug("Upload ID is null in response: $response");
        onError?.call(
          objFile,
          ErrorCode.uploadError,
          "Upload ID not found in the response data.",
        );
      }
    }
  }

  void _handleCompleteFileUploadResponse(
    FSInitFileUploadResponse response,
    UpFile objFile,
    void Function(UpFile objFile, String fileUrl)? onSuccess,
    void Function(UpFile objFile, ErrorCode code, String errorMessage)? onError,
    uploadState,
  ) {
    if (response.data.ok == false) {
      // Gọi callback `onError` nếu lỗi
      onError?.call(
        objFile,
        ErrorCode.uploadError,
        "${response.data.error?.message}: ${response.data.error?.details}",
      );
    } else {
      // Lấy URL từ `response` và gọi thành công
      final s3Url = response.data.data?.s3Url;

      if (s3Url != null) {
        if (uploadState.cachedFilePath != null) {
          cacheManager.removeFileFromCacheList(uploadState.cachedFilePath!);
        }
        objFile.metaData = response.data.data!;

        onSuccess?.call(objFile, s3Url);
      } else {
        logDebug("S3 URL is null in response: $response");
        onError?.call(
          objFile,
          ErrorCode.uploadError,
          "S3 URL not found in the response data.",
        );
      }
    }
  }

  /// Optimized file upload handler for files larger than 5MB using StreamController and back-pressure.
  ///
  /// This optimized version implements:
  /// 1. Memory-efficient chunk reading (no full file loading)
  /// 2. Stream-based back-pressure control
  /// 3. Immediate memory release after chunk upload
  /// 4. MD5 calculation in isolate
  /// 5. Rate limiting between chunks
  ///
  void _handleFileOver5Mb(
    File file,
    onProgress,
    onSuccess,
    onError,
    objFile,
    CancelToken? cancelToken,
    String folderPath,
    UploadState uploadState,
  ) async {
    logDebug("handle File Over 5Mb v2 - Optimized");
    late DateTime startTime, endTime;

    cancelToken?.whenCancel.then((_) {
      logDebug("Upload canceled by user.");
      _listenUploadCanceledByUser(
        file,
        onError,
        objFile,
        uploadState,
      );
    });

    final initFileChunkUploadMeta = InitFileChunkUploadMeta(
      type: UploadType.initFileChunkUpload,
      name: basename(file.path),
      totalChunks: uploadState.totalChunks,
      fileSize: uploadState.totalSize,
      ref: uploadState.ref,
    );

    String uploadKey = objFile.key ?? defaultKey;
    await _prepareFileChunksLite(file, uploadState);

    // Create StreamController for back-pressure management
    final chunkController = StreamController<UploadChunk>();
    int activeUploads = 0;
    final maxConcurrent = AppConfig.maxConcurrentChunks;

    _listenConnectivityChanged(file, onError, objFile, uploadState);

    errorListener(dynamic error) =>
        _errorListener(uploadState.ref ?? '', error, onError, objFile);

    late final Function(String) messageListener;

    // Setup stream listener with back-pressure control
    chunkController.stream.listen((chunk) async {
      if (activeUploads >= maxConcurrent) return;

      activeUploads++;
      try {
        await _sendChunkAsyncOptimized(file, chunk, uploadState, uploadKey);
      } finally {
        activeUploads--;

        // Add next chunk if available and not closed
        if (!chunkController.isClosed) {
          final nextChunk = uploadState.uploadChunks
              ?.where((c) => !c.isCompleted && !c.isProcessing)
              .firstOrNull;
          if (nextChunk != null) {
            nextChunk.isProcessing = true;
            chunkController.add(nextChunk);
          }
        }
      }
    });

    messageListener = (String message) async {
      Map<String, dynamic> jsonResponse = jsonDecode(message);
      //.. only listen by ref
      if (jsonResponse['ref'] != uploadState.ref) return;

      logDebug("Response for over 5mb file upload: $message");

      final response = FSFileChunkUploadResponse.fromJson(jsonResponse);
      final type = UploadType.fromString(response.type);

      switch (type) {
        case UploadType.initFileChunkUpload:
          final fileChunkUploadData =
              FSFileChunkUploadData.fromJson(response.data);

          uploadState.uploadId = fileChunkUploadData.data?.uploadId;
          uploadState.s3UploadId = fileChunkUploadData.data?.s3UploadId;
          if (fileChunkUploadData.ok == false) {
            chunkController.close();
            onError?.call(
              objFile,
              ErrorCode.uploadError,
              "${fileChunkUploadData.error?.message}: ${fileChunkUploadData.error?.details}",
            );
          } else {
            startTime = DateTime.now();

            // Add initial chunks to controller
            final initialChunks = uploadState.uploadChunks!
                .take(maxConcurrent)
                .toList();

            for (final chunk in initialChunks) {
              chunk.isProcessing = true;
              chunkController.add(chunk);
            }
          }
          break;

        case UploadType.fileChunkUpload:
          final fileChunkUploadData =
              FSFileChunkUploadDataV2.fromJson(response.data);

          if (fileChunkUploadData.ok == false) {
            chunkController.close();
            _handleUploadError(
              file,
              objFile,
              uploadState,
              onError,
              ErrorCode.uploadError,
              "${fileChunkUploadData.error?.message}: ${fileChunkUploadData.error?.details}",
            );
          } else {
            // Update chunk status and release memory immediately
            final uploadChunk = fileChunkUploadData.data;
            if (uploadChunk != null && uploadChunk.isCompleted == true) {
              final chunk = uploadState.uploadChunks
                  ?.where((uc) => uc.chunkIndex == uploadChunk.chunkIndex)
                  .single;

              if (chunk != null) {
                chunk.isCompleted = true;
                chunk.isProcessing = false;
                chunk.chunkData = null; // Immediate memory release
                uploadState.wsState?.currentChunks -= 1;
              }
            }

            // Update progress
            final numChunkCompleted = uploadState.uploadChunks
                ?.where((uc) => uc.isCompleted == true)
                .length ?? 0;

            final progress = numChunkCompleted / uploadState.totalChunks;
            onProgress?.call(progress);

            // Check if all chunks are completed
            if (numChunkCompleted == uploadState.uploadChunks?.length) {
              chunkController.close();
              final completeFileChunkMeta = CompleteFileChunkMeta(
                type: UploadType.completeFileChunkUpload,
                uploadId: uploadState.uploadId!,
                s3UploadId: uploadState.s3UploadId!,
                ref: uploadState.ref,
              );

              _webSocketManager.sendMessage(
                uploadKey,
                uploadState.ref ?? '',
                completeFileChunkMeta.toJsonEncode(),
              );
            }
          }
          break;

        case UploadType.completeFileChunkUpload:
          final completeFileChunkData =
              FSCompleteFileChunkData.fromJson(response.data);
          if (completeFileChunkData.ok == false) {
            chunkController.close();
            _handleUploadError(
              file,
              objFile,
              uploadState,
              onError,
              ErrorCode.uploadError,
              "${completeFileChunkData.error?.message}: ${completeFileChunkData.error?.details}",
            );
          } else {
            final s3Url = completeFileChunkData.data?.s3Url;

            if (uploadState.cachedFilePath != null) {
              cacheManager.removeFileFromCacheList(uploadState.cachedFilePath!);
            }
            objFile.metaData = completeFileChunkData.data;
            onSuccess?.call(objFile, s3Url!);

            endTime = DateTime.now();
            uploadState.setUploadSpeed(startTime, endTime);
          }

          uploadState.wsState?.status == WebSocketStatus.idle;
          _webSocketManager.updateConnectionsActivityByCondition(
            uploadKey,
            WebSocketStatus.idle,
            ((state) => state.ref == uploadState.ref),
          );
          break;
        default:
          logDebug("Unknown response type: ${response.type}");
          break;
      }
    };

    _webSocketManager.addErrorListener(uploadKey, errorListener);
    _webSocketManager.sendMessage(
      uploadKey,
      uploadState.ref ?? '',
      initFileChunkUploadMeta.toJsonEncode(),
    );
    _webSocketManager.addMessageListener(uploadKey, messageListener);
  }

  /// Optimized chunk sending with memory management and rate limiting
  Future<void> _sendChunkAsyncOptimized(
    File file,
    UploadChunk chunk,
    UploadState uploadState,
    String uploadKey,
  ) async {
    try {
      // Calculate MD5 in isolate to avoid blocking UI thread
      chunk.md5ChunkHash = await compute(
        _calculateMd5ForChunk,
        {
          'filePath': file.path,
          'chunkIndex': chunk.chunkIndex,
          'chunkSize': uploadState.chunkSize,
        },
      );

      // Read chunk data only when needed
      final offset = chunk.chunkIndex * uploadState.chunkSize;
      final chunkData = await _readChunk(
        file,
        offset,
        uploadState.chunkSize,
      );

      final fileMeta = FileChunkMeta(
        type: UploadType.fileChunkUpload,
        uploadId: uploadState.uploadId!,
        s3UploadId: uploadState.s3UploadId!,
        chunkIndex: chunk.chunkIndex,
        md5ChunkHash: chunk.md5ChunkHash,
        chunkData: chunkData,
        ref: uploadState.ref,
      );

      logDebug("Sending chunk ${chunk.chunkIndex}...");
      final wsState = uploadState.wsState;
      wsState?.currentChunks += 1;

      if (wsState != null &&
          wsState.currentChunks >= AppConfig.maxConcurrentChunks) {
        wsState.status = WebSocketStatus.chunkFull;
      }

      _webSocketManager.sendMessage(
        uploadKey,
        uploadState.ref ?? '',
        fileMeta.toJsonEncode(),
      );

      // Rate limiting - add small delay between chunks
      await Future.delayed(Duration(milliseconds: 10));

      // Release UI thread periodically
      await Future.delayed(Duration.zero);

    } catch (e) {
      logDebug("Error sending chunk ${chunk.chunkIndex}: $e");
      chunk.isProcessing = false;
      rethrow;
    }
  }

  void _errorListener(String ref, dynamic error, onError, file) {
    if (ref != error["ref"]) return;
    logDebug(
      "WebSocket error triggered for ${file.name}: ref: $ref ${error["error"]}",
    );
    onError?.call(
      file,
      error["error"] is String && error["error"].contains("Timeout")
          ? ErrorCode.timeoutListenWebsocket
          : ErrorCode.uploadError,
      error["error"],
    );
  }

  /// Optimized chunk preparation - only stores metadata, no chunk data
  Future<void> _prepareFileChunksLite(
    File file,
    UploadState uploadState,
  ) async {
    int chunkSize = uploadState.chunkSize;
    int totalSize = await file.length();
    int currentOffset = 0;

    uploadState.uploadChunks = [];

    while (currentOffset < totalSize) {
      final chunkIndex = currentOffset ~/ chunkSize;

      uploadState.uploadChunks?.add(
        UploadChunk(
          offset: currentOffset,
          chunkIndex: chunkIndex,
          md5ChunkHash: "",
          isCompleted: false,
          isProcessing: false,
          chunkData: null, // No data stored - will be read when needed
        ),
      );
      currentOffset += chunkSize;
    }

    logDebug(
      "Prepared ${uploadState.uploadChunks?.length ?? 0} chunks from file. Total size: $totalSize bytes.",
    );
  }

  void _uploadFileChunk(
    UploadState uploadState,
    fileBytes,
    IOWebSocketChannel channel,
    onProgress,
  ) {
    logDebug("offset: ${uploadState.offset}");

    final chunk = fileBytes.sublist(uploadState.offset, uploadState.end);

    final fileMeta = FileChunkMeta(
      type: UploadType.fileChunkUpload,
      uploadId: uploadState.uploadId!,
      s3UploadId: uploadState.s3UploadId!,
      chunkIndex: uploadState.chunkIndex,
      md5ChunkHash: AppConfig.generateMd5(chunk),
      chunkData: chunk,
    );

    channel.sink.add(fileMeta.toJsonEncode());

    final newChunk = UploadChunk(
      offset: uploadState.offset,
      chunkIndex: fileMeta.chunkIndex,
      md5ChunkHash: fileMeta.md5ChunkHash,
      isCompleted: false,
    );

    if (uploadState.uploadChunks!
        .where((chunk) => chunk.chunkIndex == fileMeta.chunkIndex)
        .isEmpty) {
      uploadState.uploadChunks?.add(newChunk);
    }

    logDebug(
      'Chunk ${uploadState.chunkIndex} of ${uploadState.totalChunks} sent: ${chunk.length} bytes. '
      'Progress: ${uploadState.chunkIndex / uploadState.totalSize} chunks: ${uploadState.uploadChunks.toString()}',
    );

    _smoothProgress(
      uploadState.offset,
      uploadState.end,
      uploadState.totalSize,
      onProgress,
    );

    uploadState.offset = uploadState.end;
    uploadState.chunkIndex++;
  }

  // Private connection

  Future<IOWebSocketChannel> _connectWithTimeout(
    String url,
    Duration timeout, {
    Map<String, dynamic>? headers,
  }) async {
    try {
      // Use Future.any to set a timeout for the connection process
      final channel = await Future.any([
        Future(
          () async => IOWebSocketChannel.connect(
            Uri.parse(url),
            headers: headers,
            pingInterval: AppConfig.pingIntervalDuration,
          ),
        ),
        Future.delayed(
          timeout,
          () => throw TimeoutException('Connection to $url timed out'),
        ),
      ]);
      return channel;
    } on TimeoutException {
      logDebug("Connection to WebSocket timed out.");
      rethrow; // Optional: Handle the exception here if desired
    } catch (e) {
      logDebug("Error connecting to WebSocket: $e");
      rethrow;
    }
  }

  // Private helper / handleError / valid

  Future<void> _handleUploadError(
    File file,
    UpFile objFile,
    UploadState? uploadState,
    onError,
    errorCode,
    errorMessage,
  ) async {
    if (uploadState != null) {
      uploadState.cachedFilePath = await cacheManager.saveFileToCache(file);
      objFile.uploadState = uploadState;

      logDebug("Error Index chunk: ${uploadState.toString()}.");
    }

    onError.call(objFile, errorCode, errorMessage);
  }

  bool _isLimitExceededFileSize(fileSize) {
    return limitFileSizeByMb == 0
        ? false
        : fileSize > limitFileSizeByMb * 1024 * 1024;
  }

  bool _isNotValidFileType(fileType) {
    return limitFileType is List<String>
        ? !limitFileType!.contains(fileType)
        : false;
  }

  void _smoothProgress(
    int start,
    int end,
    int totalSize,
    void Function(double)? onProgress,
  ) {
    const int updateSteps = 100;
    const int delayMultiplier = 5;

    final double stepProgress = (end - start) / updateSteps;

    for (int step = 0; step <= updateSteps; step++) {
      final duration = Duration(milliseconds: delayMultiplier * step);
      Future.delayed(duration, () {
        onProgress?.call((start + step * stepProgress) / totalSize);
      });
    }
  }
}

class TrackableFuture<T> {
  final Future<T> future;
  bool isCompleted = false;

  TrackableFuture(this.future) {
    future.then((_) {
      isCompleted = true;
    });
  }
}
